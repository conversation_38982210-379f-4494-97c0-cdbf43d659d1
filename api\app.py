import os
import sys
import logging
from datetime import datetime
from flask import Flask, request, jsonify, send_from_directory, make_response
from werkzeug.utils import secure_filename
from flask_cors import CORS # type: ignore
import base64
from PIL import Image
import io

# Ajuster le chemin pour les importations relatives
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(current_dir)
if parent_dir not in sys.path:
    sys.path.append(parent_dir)

# Importer les modules locaux
from model_loader import predict_image
from gemini_service import get_medical_analysis, chat_with_medical_assistant, detect_mri_scan
from translation_service import translate_results
from pdf_generator import create_pdf_report
from config import Config
from chat_manager import chat_manager

app = Flask(__name__)
app.config['JSON_AS_ASCII'] = False
app.config['JSONIFY_MIMETYPE'] = 'application/json;charset=utf-8'
CORS(app)

# Load configuration
Config.validate_config()

# Configure logging
logging.basicConfig(
    level=getattr(logging, Config.LOG_LEVEL),
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(Config.LOG_FILE),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration des chemins
BASE_DIR = os.path.abspath(os.path.dirname(__file__))
UPLOAD_FOLDER = os.path.join(BASE_DIR, '..', 'uploads')
ALLOWED_EXTENSIONS = Config.ALLOWED_EXTENSIONS

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = Config.MAX_CONTENT_LENGTH
app.config['SECRET_KEY'] = Config.SECRET_KEY

# Créer le dossier uploads et logs s'ils n'existent pas
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(os.path.dirname(Config.LOG_FILE), exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

@app.route('/')
def home():
    return send_from_directory('../templates', 'index.html')

@app.route('/mri-test')
def mri_test():
    return send_from_directory('../templates', 'mri_detection_test.html')

@app.route('/uploads/<path:filename>')
def serve_upload(filename):
    return send_from_directory(app.config['UPLOAD_FOLDER'], filename)

@app.route('/api/predict', methods=['POST'])
def predict():
    if 'file' not in request.files:
        return jsonify({'error': 'Aucun fichier fourni'}), 400

    file = request.files['file']
    if file.filename == '':
        return jsonify({'error': 'Aucun fichier sélectionné'}), 400

    if not allowed_file(file.filename):
        return jsonify({'error': 'Type de fichier non autorisé'}), 400

    try:
        # Générer un nom de fichier unique
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        filename = f"{timestamp}_{secure_filename(file.filename)}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)

        # Sauvegarder le fichier
        file.save(filepath)

        # Faire la prédiction
        result = predict_image(filepath)

        return jsonify({
            **result,
            'image_url': f"/uploads/{filename}?t={timestamp}"
        })

    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/analyze', methods=['POST'])
def analyze_image():
    if 'image' not in request.json:
        return jsonify({'error': 'No image provided'}), 400

    try:
        # Decode base64 image
        image_data = request.json['image']
        # Remove data URL prefix if present
        if ',' in image_data:
            image_data = image_data.split(',')[1]

        image = Image.open(io.BytesIO(base64.b64decode(image_data)))

        # Get model type from request (default to 'brain')
        model_type = request.json.get('model_type', 'brain')

        # Get prediction from model
        prediction = predict_image(image, model_type=model_type)

        # Ensure proper encoding for all text fields
        for key in prediction:
            if isinstance(prediction[key], str):
                # Ensure proper UTF-8 encoding
                prediction[key] = prediction[key].encode('latin1').decode('utf-8') if is_latin1_encoded(prediction[key]) else prediction[key]

        # Get analysis from Gemini API
        try:
            analysis = get_medical_analysis(prediction)
            # Ensure proper encoding for analysis fields
            for key in analysis:
                if isinstance(analysis[key], str):
                    analysis[key] = analysis[key].encode('latin1').decode('utf-8') if is_latin1_encoded(analysis[key]) else analysis[key]
        except Exception as e:
            print(f"Error getting analysis: {e}")
            analysis = {
                "summary": f"L'analyse a identifié {prediction['class_name']} avec une confiance de {prediction['confidence']}%.",
                "recommendations": "Consultez un professionnel de santé pour une évaluation complète."
            }

        # Combine results
        result = {
            **prediction,
            "analysis": analysis
        }

        # Créer ou associer une session de chat avec les résultats d'analyse
        session_id = request.json.get('session_id')
        if not session_id:
            # Créer une nouvelle session si aucune n'est fournie
            session_id = chat_manager.create_session(model_type)

        # Associer les données d'analyse à la session
        image_info = {
            'model_type': model_type,
            'timestamp': datetime.now().isoformat(),
            'analysis_type': 'base64_image'
        }

        chat_manager.set_analysis_data(session_id, result, image_info)

        # Ajouter l'ID de session à la réponse
        result['chat_session_id'] = session_id

        response = jsonify(result)
        response.headers['Content-Type'] = 'application/json;charset=utf-8'
        return response
    except Exception as e:
        print(f"Error in analyze_image: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """Endpoint de chat amélioré avec gestion de session"""
    try:
        # Validation des données d'entrée
        if not request.json:
            return jsonify({'error': 'Données JSON requises'}), 400

        if 'message' not in request.json:
            return jsonify({'error': 'Message requis'}), 400

        user_message = request.json['message'].strip()
        if not user_message:
            return jsonify({'error': 'Message vide'}), 400

        # Récupérer les paramètres
        session_id = request.json.get('session_id')
        model_type = request.json.get('model_type', 'brain')

        # Obtenir ou créer une session
        session_id, session = chat_manager.get_or_create_session(session_id, model_type)

        # Ajouter le message utilisateur à la session
        chat_manager.add_message(session_id, 'user', user_message)

        # Obtenir le contexte de conversation et d'analyse
        conversation_context = chat_manager.get_context(session_id, max_messages=8)
        analysis_context = chat_manager.get_analysis_context(session_id)

        # Appeler l'API Gemini avec le contexte complet
        gemini_response = chat_with_medical_assistant(
            user_message,
            conversation_context,
            model_type,
            analysis_context
        )

        # Vérifier le statut de la réponse
        if gemini_response['status'] != 'success':
            logger.warning(f"Erreur Gemini dans session {session_id}: {gemini_response.get('error_code')}")
            return jsonify({
                'response': gemini_response['message'],
                'session_id': session_id,
                'status': gemini_response['status'],
                'error_code': gemini_response.get('error_code')
            }), 200

        # Ajouter la réponse de l'assistant à la session
        assistant_message = gemini_response['message']
        chat_manager.add_message(session_id, 'assistant', assistant_message)

        # Préparer la réponse améliorée
        response_data = {
            'response': assistant_message,
            'session_id': session_id,
            'status': 'success',
            'model_type': model_type,
            'message_count': len(session.messages),
            'has_analysis': chat_manager.has_analysis_data(session_id),
            'analysis_summary': chat_manager.get_analysis_summary(session_id) if chat_manager.has_analysis_data(session_id) else None,
            'response_metadata': {
                'response_length': len(assistant_message),
                'context_used': bool(conversation_context or analysis_context),
                'timestamp': datetime.now().isoformat()
            }
        }

        logger.info(f"Chat réussi - Session: {session_id}, Messages: {len(session.messages)}, Analyse liée: {chat_manager.has_analysis_data(session_id)}")
        return jsonify(response_data)

    except Exception as e:
        logger.error(f"Erreur dans l'endpoint chat: {e}")
        return jsonify({
            'error': 'Erreur interne du serveur',
            'response': 'Je rencontre des difficultés techniques. Veuillez réessayer.'
        }), 500

# Endpoint /api/chat/suggestions supprimé - les suggestions ne sont plus utilisées
# dans la nouvelle interface améliorée du chatbot

@app.route('/api/chat/connect-analysis', methods=['POST'])
def connect_analysis_to_chat():
    """Endpoint pour connecter une analyse d'image au chat"""
    try:
        data = request.json
        session_id = data.get('session_id')
        analysis_data = data.get('analysis_data')
        image_info = data.get('image_info', {})
        model_type = data.get('model_type', 'brain')

        if not analysis_data:
            return jsonify({
                'error': 'Données d\'analyse manquantes',
                'success': False
            }), 400

        # Créer ou récupérer la session
        if not session_id:
            session_id = chat_manager.create_session(model_type)

        # Associer les données d'analyse à la session
        success = chat_manager.set_analysis_data(session_id, analysis_data, image_info)

        if success:
            # Ajouter un message système pour informer de la connexion
            system_message = f"Analyse d'image connectée: {analysis_data.get('class_name', 'Condition détectée')} (confiance: {analysis_data.get('confidence', 0)}%)"
            chat_manager.add_message(session_id, 'system', system_message)

            return jsonify({
                'success': True,
                'session_id': session_id,
                'message': 'Analyse connectée avec succès au chat',
                'analysis_summary': chat_manager.get_analysis_summary(session_id)
            })
        else:
            return jsonify({
                'error': 'Impossible de connecter l\'analyse au chat',
                'success': False
            }), 500

    except Exception as e:
        logger.error(f"Erreur lors de la connexion analyse-chat: {e}")
        return jsonify({
            'error': 'Erreur interne du serveur',
            'success': False
        }), 500

@app.route('/api/chat/session', methods=['POST'])
def manage_chat_session():
    """Endpoint pour gérer les sessions de chat"""
    try:
        action = request.json.get('action')
        session_id = request.json.get('session_id')

        if action == 'create':
            model_type = request.json.get('model_type', 'brain')
            new_session_id = chat_manager.create_session(model_type)
            return jsonify({
                'session_id': new_session_id,
                'status': 'created'
            })

        elif action == 'clear' and session_id:
            success = chat_manager.clear_session_history(session_id)
            return jsonify({
                'success': success,
                'status': 'cleared' if success else 'not_found'
            })

        elif action == 'delete' and session_id:
            success = chat_manager.delete_session(session_id)
            return jsonify({
                'success': success,
                'status': 'deleted' if success else 'not_found'
            })

        elif action == 'info' and session_id:
            session = chat_manager.get_session(session_id)
            if session:
                return jsonify({
                    'session_info': session.get_summary(),
                    'status': 'found'
                })
            else:
                return jsonify({'status': 'not_found'}), 404

        else:
            return jsonify({'error': 'Action invalide ou paramètres manquants'}), 400

    except Exception as e:
        logger.error(f"Erreur dans la gestion de session: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/api/chat/stats', methods=['GET'])
def get_chat_stats():
    """Endpoint pour obtenir les statistiques des sessions de chat"""
    try:
        stats = chat_manager.get_session_stats()
        return jsonify(stats)
    except Exception as e:
        logger.error(f"Erreur lors de la récupération des statistiques: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/api/chat/link-analysis', methods=['POST'])
def link_analysis_to_chat():
    """Endpoint pour associer des résultats d'analyse à une session de chat"""
    try:
        if not request.json:
            return jsonify({'error': 'Données JSON requises'}), 400

        session_id = request.json.get('session_id')
        analysis_data = request.json.get('analysis_data')

        if not session_id or not analysis_data:
            return jsonify({'error': 'session_id et analysis_data requis'}), 400

        # Vérifier que la session existe
        session = chat_manager.get_session(session_id)
        if not session:
            return jsonify({'error': 'Session introuvable'}), 404

        # Associer les données d'analyse
        image_info = {
            'timestamp': datetime.now().isoformat(),
            'linked_manually': True
        }

        success = chat_manager.set_analysis_data(session_id, analysis_data, image_info)

        if success:
            return jsonify({
                'success': True,
                'message': 'Analyse associée avec succès',
                'session_id': session_id
            })
        else:
            return jsonify({'error': 'Impossible d\'associer l\'analyse'}), 500

    except Exception as e:
        logger.error(f"Erreur lors de l'association d'analyse: {e}")
        return jsonify({'error': 'Erreur interne du serveur'}), 500

@app.route('/api/translate', methods=['POST'])
def translate_api():
    """
    API endpoint to translate results to different languages
    """
    if not request.json or 'data' not in request.json or 'target_language' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        target_language = request.json['target_language']

        # Valider la langue cible
        if target_language not in ['fr', 'en', 'ar']:
            return jsonify({'error': 'Unsupported language'}), 400

        # Si la langue est déjà en français et c'est ce qui est demandé, retourner les données telles quelles
        if target_language == 'fr' and data.get('language', 'fr') == 'fr':
            # Assurons-nous que le champ language est correctement défini
            data['language'] = 'fr'
            return jsonify(data)

        # Traduire les résultats
        translated_data = translate_results(data, target_language)

        # Ajouter l'information de langue aux données
        translated_data['language'] = target_language

        # Vérifier l'encodage des données traduites
        for key in translated_data:
            if isinstance(translated_data[key], str):
                # Assurons-nous que les chaînes sont correctement encodées en UTF-8
                if is_latin1_encoded(translated_data[key]):
                    translated_data[key] = translated_data[key].encode('latin1').decode('utf-8')

        # Vérifier également l'encodage des données d'analyse
        if 'analysis' in translated_data:
            for key in translated_data['analysis']:
                if isinstance(translated_data['analysis'][key], str):
                    if is_latin1_encoded(translated_data['analysis'][key]):
                        translated_data['analysis'][key] = translated_data['analysis'][key].encode('latin1').decode('utf-8')

        return jsonify(translated_data)
    except Exception as e:
        print(f"Error in translate_api: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/generate-pdf', methods=['POST'])
def generate_pdf():
    """
    Générer un rapport PDF avec les résultats d'analyse en trois langues
    """
    if not request.json or 'data' not in request.json or 'image' not in request.json:
        return jsonify({'error': 'Invalid request data'}), 400

    try:
        data = request.json['data']
        image_data = request.json['image']
        site_name = request.json.get('site_name', 'MedScan AI')

        # Générer le PDF
        pdf_data = create_pdf_report(data, image_data, site_name)

        # Créer une réponse avec le PDF
        response = make_response(pdf_data)
        response.headers['Content-Type'] = 'application/pdf'
        response.headers['Content-Disposition'] = f'attachment; filename=rapport_medical_{datetime.now().strftime("%Y%m%d%H%M%S")}.pdf'

        return response
    except Exception as e:
        print(f"Error generating PDF: {e}")
        return jsonify({'error': str(e)}), 500

@app.route('/api/detect-mri', methods=['POST'])
def detect_mri():
    """
    Endpoint to detect if an uploaded image is an MRI scan using Gemini Vision API

    Accepts:
    - File upload (multipart/form-data)
    - Base64 image data (JSON)

    Returns:
    - JSON response with MRI detection results
    """
    try:
        logger.info("MRI detection request received")

        # Handle file upload
        if 'file' in request.files:
            file = request.files['file']
            if file.filename == '':
                return jsonify({'error': 'No file selected'}), 400

            if not allowed_file(file.filename):
                return jsonify({'error': 'Invalid file type. Allowed: PNG, JPG, JPEG, WebP'}), 400

            # Save uploaded file temporarily
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            filename = f"mri_detection_{timestamp}_{secure_filename(file.filename)}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Determine image format
            image_format = file.filename.rsplit('.', 1)[1].upper()
            if image_format == 'JPG':
                image_format = 'JPEG'

            # Perform MRI detection
            result = detect_mri_scan(filepath, image_format)

            # Add image URL to response
            result['image_url'] = f"/uploads/{filename}?t={timestamp}"

            logger.info(f"MRI detection completed for file upload: {result['is_mri']}")

        # Handle base64 image data
        elif request.json and 'image' in request.json:
            image_data = request.json['image']
            image_format = request.json.get('format', 'JPEG').upper()

            if image_format == 'JPG':
                image_format = 'JPEG'

            # Perform MRI detection
            result = detect_mri_scan(image_data, image_format)

            logger.info(f"MRI detection completed for base64 image: {result['is_mri']}")

        else:
            return jsonify({'error': 'No image data provided. Use file upload or base64 image data.'}), 400

        # Add metadata to response
        result['timestamp'] = datetime.now().isoformat()
        result['confidence_threshold'] = Config.MRI_DETECTION_CONFIDENCE_THRESHOLD
        result['meets_threshold'] = result['confidence'] >= Config.MRI_DETECTION_CONFIDENCE_THRESHOLD

        # Set appropriate HTTP status based on API status
        status_code = 200
        if result.get('api_status') == 'error':
            status_code = 500
        elif result.get('api_status') == 'partial_success':
            status_code = 206  # Partial Content

        return jsonify(result), status_code

    except Exception as e:
        logger.error(f"Error in MRI detection endpoint: {str(e)}")
        return jsonify({
            'error': 'Internal server error during MRI detection',
            'details': str(e),
            'is_mri': False,
            'confidence': 0.0,
            'api_status': 'error'
        }), 500

# Fonction utilitaire pour détecter si une chaîne est encodée en latin1
def is_latin1_encoded(text):
    try:
        return text.encode('latin1').decode('utf-8') != text
    except:
        return False

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000, debug=True)
