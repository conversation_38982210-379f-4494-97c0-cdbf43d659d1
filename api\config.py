import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration class for the Flask application"""

    # Gemini API Configuration
    GEMINI_API_KEY = os.getenv('GEMINI_API_KEY', 'AIzaSyB9V45g5Ax-Voqjw1J7X61P2gJqkWagS_4')  # Fallback for existing setup
    GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={GEMINI_API_KEY}"
    GEMINI_VISION_MODEL = "gemini-1.5-flash"

    # Flask Configuration
    SECRET_KEY = os.getenv('SECRET_KEY', 'dev-secret-key-change-in-production')
    DEBUG = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    # File Upload Configuration
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB
    ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'webp'}

    # Logging Configuration
    LOG_LEVEL = os.getenv('LOG_LEVEL', 'INFO')
    LOG_FILE = os.getenv('LOG_FILE', 'logs/app.log')

    # MRI Detection Configuration
    MRI_DETECTION_CONFIDENCE_THRESHOLD = float(os.getenv('MRI_CONFIDENCE_THRESHOLD', '0.7'))

    # Chat Configuration - Nouvelles configurations
    CHAT_SESSION_TIMEOUT_HOURS = int(os.getenv('CHAT_SESSION_TIMEOUT_HOURS', '24'))
    CHAT_MAX_CONTEXT_LENGTH = int(os.getenv('CHAT_MAX_CONTEXT_LENGTH', '4000'))
    CHAT_MAX_MESSAGES_IN_CONTEXT = int(os.getenv('CHAT_MAX_MESSAGES_IN_CONTEXT', '10'))
    CHAT_ENABLE_ANALYSIS_LINKING = os.getenv('CHAT_ENABLE_ANALYSIS_LINKING', 'True').lower() == 'true'

    # Performance Configuration
    ENABLE_CACHING = os.getenv('ENABLE_CACHING', 'True').lower() == 'true'
    CACHE_TIMEOUT = int(os.getenv('CACHE_TIMEOUT', '300'))  # 5 minutes

    # Security Configuration
    RATE_LIMIT_PER_MINUTE = int(os.getenv('RATE_LIMIT_PER_MINUTE', '60'))
    ENABLE_CORS = os.getenv('ENABLE_CORS', 'True').lower() == 'true'

    # Feature Flags
    ENABLE_CHAT_SUGGESTIONS = False  # Désactivé dans la nouvelle version
    ENABLE_ENHANCED_CHAT = True
    ENABLE_ANALYSIS_CHAT_INTEGRATION = True
    ENABLE_REAL_TIME_NOTIFICATIONS = True

    @staticmethod
    def validate_config():
        """Validate that required configuration is present"""
        if not Config.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY environment variable is required")

        # Validation des nouvelles configurations
        if Config.CHAT_SESSION_TIMEOUT_HOURS <= 0:
            raise ValueError("CHAT_SESSION_TIMEOUT_HOURS must be positive")

        if Config.CHAT_MAX_CONTEXT_LENGTH <= 0:
            raise ValueError("CHAT_MAX_CONTEXT_LENGTH must be positive")

        if Config.RATE_LIMIT_PER_MINUTE <= 0:
            raise ValueError("RATE_LIMIT_PER_MINUTE must be positive")

        return True

    @staticmethod
    def get_feature_flags():
        """Retourne les feature flags actifs"""
        return {
            'chat_suggestions': Config.ENABLE_CHAT_SUGGESTIONS,
            'enhanced_chat': Config.ENABLE_ENHANCED_CHAT,
            'analysis_chat_integration': Config.ENABLE_ANALYSIS_CHAT_INTEGRATION,
            'real_time_notifications': Config.ENABLE_REAL_TIME_NOTIFICATIONS,
            'caching': Config.ENABLE_CACHING
        }

    @staticmethod
    def get_chat_config():
        """Retourne la configuration du chat"""
        return {
            'session_timeout_hours': Config.CHAT_SESSION_TIMEOUT_HOURS,
            'max_context_length': Config.CHAT_MAX_CONTEXT_LENGTH,
            'max_messages_in_context': Config.CHAT_MAX_MESSAGES_IN_CONTEXT,
            'enable_analysis_linking': Config.CHAT_ENABLE_ANALYSIS_LINKING
        }
