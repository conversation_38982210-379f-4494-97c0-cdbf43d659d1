#!/usr/bin/env python3
"""
Application Flask pour MediScanAI
"""

from flask import Flask, render_template, request, jsonify, send_from_directory
import os
import json
import base64
from datetime import datetime

app = Flask(__name__)

# Configuration
app.config['SECRET_KEY'] = 'mediscanai-secret-key-2025'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

@app.route('/')
def index():
    """Page d'accueil"""
    return render_template('index.html')

@app.route('/static/<path:filename>')
def static_files(filename):
    """Servir les fichiers statiques"""
    return send_from_directory('static', filename)

@app.route('/favicon.ico')
def favicon():
    """Servir le favicon"""
    return send_from_directory('static', 'favicon.ico')

@app.route('/api/analyze', methods=['POST'])
def analyze_image():
    """API pour analyser une image médicale"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'Aucune image fournie'}), 400
        
        file = request.files['image']
        model_type = request.form.get('model', 'brain')
        
        if file.filename == '':
            return jsonify({'error': 'Aucun fichier sélectionné'}), 400
        
        # Simulation d'analyse (remplacer par vraie IA)
        import time
        time.sleep(2)  # Simulation du temps d'analyse
        
        # Résultats simulés basés sur le type de modèle
        results = get_simulated_results(model_type)
        
        return jsonify({
            'success': True,
            'results': results,
            'model_used': model_type,
            'analysis_time': '2.3s',
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/api/chat', methods=['POST'])
def chat():
    """API pour le chatbot médical"""
    try:
        data = request.get_json()
        message = data.get('message', '')
        analysis_context = data.get('analysis_context', None)
        
        # Simulation de réponse du chatbot
        response = generate_chat_response(message, analysis_context)
        
        return jsonify({
            'success': True,
            'response': response,
            'timestamp': datetime.now().isoformat()
        })
        
    except Exception as e:
        return jsonify({'error': str(e)}), 500

def get_simulated_results(model_type):
    """Génère des résultats simulés selon le type de modèle"""
    results_templates = {
        'brain': {
            'summary': 'Analyse cérébrale complétée. Aucune anomalie majeure détectée.',
            'conditions': [
                {
                    'name': 'Structure normale',
                    'confidence': 94.2,
                    'severity': 'normal',
                    'description': 'Les structures cérébrales apparaissent normales sans signe d\'anomalie significative.'
                }
            ],
            'recommendations': [
                'Suivi de routine recommandé',
                'Aucune intervention immédiate nécessaire'
            ]
        },
        'oral': {
            'summary': 'Analyse buccale terminée. Quelques zones d\'attention identifiées.',
            'conditions': [
                {
                    'name': 'Plaque dentaire',
                    'confidence': 87.5,
                    'severity': 'mild',
                    'description': 'Présence de plaque dentaire sur plusieurs dents.'
                },
                {
                    'name': 'Gingivite légère',
                    'confidence': 72.3,
                    'severity': 'mild',
                    'description': 'Inflammation légère des gencives observée.'
                }
            ],
            'recommendations': [
                'Améliorer l\'hygiène dentaire',
                'Consultation dentaire recommandée'
            ]
        },
        'alzheimer': {
            'summary': 'Analyse cognitive complétée. Paramètres dans la normale.',
            'conditions': [
                {
                    'name': 'Fonction cognitive normale',
                    'confidence': 91.8,
                    'severity': 'normal',
                    'description': 'Les marqueurs cognitifs analysés sont dans les paramètres normaux.'
                }
            ],
            'recommendations': [
                'Maintenir une activité cognitive régulière',
                'Suivi annuel recommandé'
            ]
        },
        'fracture': {
            'summary': 'Analyse osseuse terminée. Aucune fracture détectée.',
            'conditions': [
                {
                    'name': 'Intégrité osseuse',
                    'confidence': 96.1,
                    'severity': 'normal',
                    'description': 'Aucun signe de fracture ou de lésion osseuse visible.'
                }
            ],
            'recommendations': [
                'Aucune intervention nécessaire',
                'Repos et surveillance si douleur persiste'
            ]
        }
    }
    
    return results_templates.get(model_type, results_templates['brain'])

def generate_chat_response(message, analysis_context=None):
    """Génère une réponse du chatbot"""
    message_lower = message.lower()
    
    # Réponses contextuelles si une analyse est connectée
    if analysis_context:
        if 'résultat' in message_lower or 'analyse' in message_lower:
            return "Basé sur votre analyse récente, je peux vous expliquer les résultats en détail. Que souhaitez-vous savoir spécifiquement ?"
        elif 'recommandation' in message_lower:
            return "Selon les résultats de votre analyse, voici mes recommandations personnalisées..."
    
    # Réponses générales
    if 'bonjour' in message_lower or 'salut' in message_lower:
        return "Bonjour ! Je suis votre assistant médical IA. Comment puis-je vous aider aujourd'hui ?"
    elif 'aide' in message_lower or 'help' in message_lower:
        return "Je peux vous aider avec l'interprétation de vos analyses médicales, répondre à vos questions sur les résultats, et vous fournir des informations générales sur la santé."
    elif 'merci' in message_lower:
        return "Je vous en prie ! N'hésitez pas si vous avez d'autres questions."
    else:
        return "Je comprends votre question. Basé sur les informations médicales disponibles, je recommande de consulter un professionnel de santé pour une évaluation personnalisée."

if __name__ == '__main__':
    print("🚀 Démarrage de MediScanAI...")
    print("📍 URL: http://localhost:5000")
    print("🔧 Mode: Développement")
    print("=" * 50)
    
    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )
