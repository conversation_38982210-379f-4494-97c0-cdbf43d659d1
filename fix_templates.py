#!/usr/bin/env python3
"""
Script pour corriger les problèmes de templates Flask
Corrige les chemins des fichiers statiques et vérifie la configuration
"""

import os
import re
from pathlib import Path

def fix_template_paths():
    """Corrige les chemins dans le template HTML"""
    template_file = Path("templates/index.html")
    
    if not template_file.exists():
        print("❌ Fichier template non trouvé")
        return False
    
    print("🔧 Correction des chemins dans le template...")
    
    # Lire le contenu
    with open(template_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Corrections à appliquer
    corrections = [
        # Corriger les liens CSS
        (r'href="\{\{\s*url_for\([^}]+\)\s*\}\}"', lambda m: fix_url_for(m.group(0))),
        # Corriger les liens JS
        (r'src="\{\{\s*url_for\([^}]+\)\s*\}\}"', lambda m: fix_url_for(m.group(0))),
    ]
    
    original_content = content
    
    for pattern, replacement in corrections:
        content = re.sub(pattern, replacement, content)
    
    # Vérifier si des changements ont été faits
    if content != original_content:
        # Sauvegarder le fichier corrigé
        with open(template_file, 'w', encoding='utf-8') as f:
            f.write(content)
        print("✅ Template corrigé")
        return True
    else:
        print("ℹ️  Template déjà correct")
        return True

def fix_url_for(url_for_string):
    """Convertit un url_for en chemin statique direct"""
    # Extraire le nom du fichier
    if "tailwind-local.css" in url_for_string:
        return 'href="/static/css/tailwind-local.css"'
    elif "enhanced-styles.css" in url_for_string:
        return 'href="/static/css/enhanced-styles.css"'
    elif "chat-enhancements.js" in url_for_string:
        return 'src="/static/js/chat-enhancements.js"'
    else:
        # Fallback: extraire le chemin du filename
        match = re.search(r"filename='([^']+)'", url_for_string)
        if match:
            filename = match.group(1)
            if url_for_string.startswith('href='):
                return f'href="/static/{filename}"'
            elif url_for_string.startswith('src='):
                return f'src="/static/{filename}"'
    
    return url_for_string

def check_static_files():
    """Vérifie que tous les fichiers statiques existent"""
    print("📁 Vérification des fichiers statiques...")
    
    required_files = [
        "static/css/tailwind-local.css",
        "static/css/enhanced-styles.css", 
        "static/js/chat-enhancements.js"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"  ❌ {file_path}")
    
    if missing_files:
        print(f"\n⚠️  {len(missing_files)} fichier(s) manquant(s)")
        return False
    
    print("✅ Tous les fichiers statiques sont présents")
    return True

def check_flask_config():
    """Vérifie la configuration Flask"""
    print("⚙️  Vérification de la configuration Flask...")
    
    app_file = Path("api/app.py")
    if not app_file.exists():
        print("❌ Fichier api/app.py non trouvé")
        return False
    
    with open(app_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    checks = [
        ("render_template", "Import render_template"),
        ("template_folder='../templates'", "Configuration template_folder"),
        ("static_folder='../static'", "Configuration static_folder"),
        ("def serve_static", "Route pour fichiers statiques"),
    ]
    
    all_good = True
    
    for check, description in checks:
        if check in content:
            print(f"  ✅ {description}")
        else:
            print(f"  ❌ {description}")
            all_good = False
    
    return all_good

def create_simple_favicon():
    """Crée un favicon simple si il n'existe pas"""
    favicon_path = Path("static/favicon.ico")
    
    if favicon_path.exists():
        print("✅ Favicon déjà présent")
        return True
    
    print("🎨 Création d'un favicon simple...")
    
    # Créer un favicon ICO simple (16x16 pixels, bleu)
    ico_data = bytes([
        0x00, 0x00, 0x01, 0x00, 0x01, 0x00, 0x10, 0x10, 0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x68, 0x04,
        0x00, 0x00, 0x16, 0x00, 0x00, 0x00, 0x28, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x20, 0x00,
        0x00, 0x00, 0x01, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x12, 0x0B,
        0x00, 0x00, 0x12, 0x0B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    ])
    
    # Ajouter les données de pixels (16x16 = 256 pixels, 4 bytes par pixel)
    # Couleur bleue (#4361ee) avec alpha
    blue_pixel = bytes([0xee, 0x61, 0x43, 0xff])  # BGRA format
    pixel_data = blue_pixel * 256
    
    ico_data += pixel_data
    
    # Ajouter le masque AND (32 bytes pour 16x16 pixels, 1 bit par pixel)
    and_mask = bytes([0x00] * 32)
    ico_data += and_mask
    
    try:
        with open(favicon_path, 'wb') as f:
            f.write(ico_data)
        print("✅ Favicon créé")
        return True
    except Exception as e:
        print(f"❌ Erreur lors de la création du favicon: {e}")
        return False

def main():
    """Fonction principale"""
    print("🔧 Correction des problèmes de templates Flask")
    print("=" * 50)
    
    success = True
    
    # 1. Corriger les chemins dans le template
    if not fix_template_paths():
        success = False
    
    # 2. Vérifier les fichiers statiques
    if not check_static_files():
        success = False
    
    # 3. Vérifier la configuration Flask
    if not check_flask_config():
        success = False
    
    # 4. Créer un favicon si nécessaire
    if not create_simple_favicon():
        success = False
    
    print("\n" + "=" * 50)
    
    if success:
        print("✅ Toutes les corrections ont été appliquées avec succès!")
        print("\n🚀 Prochaines étapes:")
        print("  1. Redémarrez le serveur Flask")
        print("  2. Videz le cache du navigateur (Ctrl+Shift+R)")
        print("  3. Rechargez la page")
    else:
        print("❌ Certaines corrections ont échoué")
        print("💡 Vérifiez les erreurs ci-dessus et corrigez manuellement")
    
    return success

if __name__ == "__main__":
    main()
