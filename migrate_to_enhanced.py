#!/usr/bin/env python3
"""
Script de migration vers MediScanAI Enhanced
Migre les données et configurations de l'ancienne version
"""

import os
import json
import shutil
import sqlite3
from datetime import datetime
from pathlib import Path

def print_header(title):
    """Affiche un en-tête de section"""
    print(f"\n{'='*60}")
    print(f"🔄 {title}")
    print(f"{'='*60}")

def backup_existing_data():
    """Sauvegarde les données existantes"""
    print_header("Sauvegarde des Données Existantes")
    
    backup_dir = Path(f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
    backup_dir.mkdir(exist_ok=True)
    
    # Fichiers à sauvegarder
    files_to_backup = [
        'api/app.py',
        'api/gemini_service.py',
        'api/chat_manager.py',
        'api/config.py',
        'templates/index.html',
        '.env'
    ]
    
    backed_up_files = []
    
    for file_path in files_to_backup:
        source = Path(file_path)
        if source.exists():
            dest = backup_dir / source.name
            shutil.copy2(source, dest)
            backed_up_files.append(file_path)
            print(f"  ✅ Sauvegardé: {file_path}")
        else:
            print(f"  ⚠️  Non trouvé: {file_path}")
    
    # Sauvegarder les logs si ils existent
    logs_dir = Path("logs")
    if logs_dir.exists():
        backup_logs_dir = backup_dir / "logs"
        shutil.copytree(logs_dir, backup_logs_dir)
        print(f"  ✅ Sauvegardé: logs/")
    
    print(f"\n📁 Sauvegarde créée dans: {backup_dir}")
    print(f"📊 Fichiers sauvegardés: {len(backed_up_files)}")
    
    return backup_dir

def migrate_configuration():
    """Migre la configuration vers la nouvelle version"""
    print_header("Migration de la Configuration")
    
    env_file = Path(".env")
    config_migrated = False
    
    if env_file.exists():
        print("  📄 Lecture de la configuration existante...")
        
        # Lire la configuration existante
        existing_config = {}
        with open(env_file, 'r') as f:
            for line in f:
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    existing_config[key] = value
        
        # Nouvelles configurations à ajouter
        new_configs = {
            'CHAT_SESSION_TIMEOUT_HOURS': '24',
            'CHAT_MAX_CONTEXT_LENGTH': '4000',
            'CHAT_MAX_MESSAGES_IN_CONTEXT': '10',
            'CHAT_ENABLE_ANALYSIS_LINKING': 'True',
            'ENABLE_CACHING': 'True',
            'CACHE_TIMEOUT': '300',
            'RATE_LIMIT_PER_MINUTE': '60',
            'ENABLE_CORS': 'True'
        }
        
        # Ajouter les nouvelles configurations si elles n'existent pas
        updated_config = existing_config.copy()
        for key, default_value in new_configs.items():
            if key not in updated_config:
                updated_config[key] = default_value
                print(f"  ➕ Ajouté: {key}={default_value}")
                config_migrated = True
        
        # Réécrire le fichier .env
        if config_migrated:
            with open(env_file, 'w') as f:
                f.write("# Configuration MediScanAI Enhanced\n")
                f.write("# Migré automatiquement\n\n")
                
                # Configurations existantes
                f.write("# Configurations existantes\n")
                for key, value in existing_config.items():
                    f.write(f"{key}={value}\n")
                
                # Nouvelles configurations
                f.write("\n# Nouvelles configurations Enhanced\n")
                for key, value in new_configs.items():
                    if key not in existing_config:
                        f.write(f"{key}={value}\n")
            
            print("  ✅ Configuration mise à jour")
        else:
            print("  ℹ️  Configuration déjà à jour")
    else:
        print("  ⚠️  Fichier .env non trouvé, création d'un nouveau...")
        
        # Créer un nouveau fichier .env
        with open(env_file, 'w') as f:
            f.write("""# Configuration MediScanAI Enhanced
GEMINI_API_KEY=your_gemini_api_key_here
FLASK_DEBUG=True
LOG_LEVEL=INFO

# Nouvelles configurations chat
CHAT_SESSION_TIMEOUT_HOURS=24
CHAT_MAX_CONTEXT_LENGTH=4000
CHAT_MAX_MESSAGES_IN_CONTEXT=10
CHAT_ENABLE_ANALYSIS_LINKING=True

# Performance
ENABLE_CACHING=True
CACHE_TIMEOUT=300

# Sécurité
RATE_LIMIT_PER_MINUTE=60
ENABLE_CORS=True
""")
        
        print("  ✅ Nouveau fichier .env créé")
        config_migrated = True
    
    return config_migrated

def migrate_chat_sessions():
    """Migre les sessions de chat existantes"""
    print_header("Migration des Sessions de Chat")
    
    # Chercher d'éventuelles données de session existantes
    session_files = list(Path(".").glob("*session*"))
    db_files = list(Path(".").glob("*.db"))
    
    if not session_files and not db_files:
        print("  ℹ️  Aucune session existante trouvée")
        return True
    
    print(f"  📊 Fichiers de session trouvés: {len(session_files + db_files)}")
    
    # Pour cette version, on va simplement noter les fichiers trouvés
    # Une migration plus complexe pourrait être implémentée ici
    for file_path in session_files + db_files:
        print(f"    - {file_path}")
    
    print("  ℹ️  Les sessions existantes seront préservées")
    print("  💡 Les nouvelles fonctionnalités seront disponibles pour les nouvelles sessions")
    
    return True

def update_static_files():
    """Met à jour les fichiers statiques"""
    print_header("Mise à Jour des Fichiers Statiques")
    
    # Créer les dossiers nécessaires
    static_dirs = ['static/css', 'static/js', 'static/images']
    for dir_path in static_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"  ✅ Dossier vérifié: {dir_path}")
    
    # Vérifier la présence des nouveaux fichiers
    new_files = [
        'static/css/enhanced-styles.css',
        'static/js/chat-enhancements.js'
    ]
    
    missing_files = []
    for file_path in new_files:
        if Path(file_path).exists():
            print(f"  ✅ Présent: {file_path}")
        else:
            missing_files.append(file_path)
            print(f"  ❌ Manquant: {file_path}")
    
    if missing_files:
        print(f"  ⚠️  {len(missing_files)} fichier(s) manquant(s)")
        print("  💡 Assurez-vous d'avoir tous les fichiers du projet Enhanced")
        return False
    
    return True

def verify_api_changes():
    """Vérifie que les changements API sont appliqués"""
    print_header("Vérification des Changements API")
    
    # Vérifier que l'endpoint des suggestions est supprimé
    app_file = Path("api/app.py")
    if app_file.exists():
        with open(app_file, 'r') as f:
            content = f.read()
        
        # Vérifier que les suggestions sont supprimées
        if '/api/chat/suggestions' in content and 'def get_suggestions' in content:
            print("  ⚠️  L'endpoint des suggestions semble encore présent")
            print("  💡 Vérifiez que les modifications API ont été appliquées")
            return False
        else:
            print("  ✅ Endpoint des suggestions supprimé")
        
        # Vérifier la présence du nouvel endpoint
        if '/api/chat/connect-analysis' in content:
            print("  ✅ Nouvel endpoint de connexion analyse présent")
        else:
            print("  ⚠️  Nouvel endpoint de connexion analyse manquant")
            return False
    else:
        print("  ❌ Fichier api/app.py non trouvé")
        return False
    
    return True

def create_migration_report(backup_dir, changes_made):
    """Crée un rapport de migration"""
    print_header("Création du Rapport de Migration")
    
    report_file = backup_dir / "migration_report.json"
    
    report = {
        "migration_date": datetime.now().isoformat(),
        "backup_location": str(backup_dir),
        "changes_made": changes_made,
        "version": {
            "from": "1.0.0",
            "to": "2.0.0 Enhanced"
        },
        "new_features": [
            "Interface de chat modernisée",
            "Suppression des suggestions automatiques",
            "Connexion analyse-chat intelligente",
            "Design responsive amélioré",
            "Performance optimisée"
        ],
        "breaking_changes": [
            "Endpoint /api/chat/suggestions supprimé",
            "Fonction get_chat_suggestions() supprimée",
            "Structure des réponses chat modifiée"
        ]
    }
    
    with open(report_file, 'w') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"  ✅ Rapport créé: {report_file}")
    
    # Afficher un résumé
    print(f"\n📋 Résumé de la migration:")
    print(f"  • Sauvegarde: {backup_dir}")
    print(f"  • Changements: {len(changes_made)}")
    print(f"  • Nouvelles fonctionnalités: {len(report['new_features'])}")
    print(f"  • Breaking changes: {len(report['breaking_changes'])}")
    
    return report_file

def main():
    """Fonction principale de migration"""
    print("🚀 Migration vers MediScanAI Enhanced")
    print("=====================================")
    print("Cette migration va:")
    print("  • Sauvegarder vos données existantes")
    print("  • Mettre à jour la configuration")
    print("  • Migrer les sessions de chat")
    print("  • Vérifier les nouveaux fichiers")
    print("  • Créer un rapport de migration")
    
    # Demander confirmation
    try:
        confirm = input("\n🤔 Continuer la migration? (o/N): ").strip().lower()
        if confirm not in ['o', 'oui', 'y', 'yes']:
            print("❌ Migration annulée")
            return
    except KeyboardInterrupt:
        print("\n❌ Migration annulée")
        return
    
    changes_made = []
    
    try:
        # 1. Sauvegarde
        backup_dir = backup_existing_data()
        changes_made.append("Sauvegarde créée")
        
        # 2. Migration de la configuration
        if migrate_configuration():
            changes_made.append("Configuration mise à jour")
        
        # 3. Migration des sessions
        if migrate_chat_sessions():
            changes_made.append("Sessions de chat vérifiées")
        
        # 4. Fichiers statiques
        if update_static_files():
            changes_made.append("Fichiers statiques vérifiés")
        else:
            print("⚠️  Problème avec les fichiers statiques")
        
        # 5. Changements API
        if verify_api_changes():
            changes_made.append("Changements API vérifiés")
        else:
            print("⚠️  Problème avec les changements API")
        
        # 6. Rapport de migration
        report_file = create_migration_report(backup_dir, changes_made)
        
        print_header("Migration Terminée")
        print("✅ Migration réussie!")
        print(f"📁 Sauvegarde: {backup_dir}")
        print(f"📄 Rapport: {report_file}")
        print("\n🚀 Prochaines étapes:")
        print("  1. Vérifiez votre configuration dans .env")
        print("  2. Lancez l'application avec: python start_enhanced.py")
        print("  3. Testez les nouvelles fonctionnalités")
        print("  4. Consultez ENHANCED_FEATURES.md pour plus de détails")
        
    except Exception as e:
        print(f"\n❌ Erreur lors de la migration: {e}")
        print("💡 Consultez la sauvegarde pour restaurer si nécessaire")
        return False
    
    return True

if __name__ == "__main__":
    main()
