#!/usr/bin/env python3
"""
Script de démarrage pour MediScanAI avec améliorations
Lance l'application avec les nouvelles fonctionnalités activées
"""

import os
import sys
import subprocess
import time
from pathlib import Path

def print_banner():
    """Affiche la bannière de démarrage"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                    MediScanAI Enhanced                      ║
    ║                  Version 2.0.0 - Améliorée                 ║
    ╠══════════════════════════════════════════════════════════════╣
    ║  🚀 Nouvelles fonctionnalités:                              ║
    ║  • Interface de chat modernisée                             ║
    ║  • Suppression des suggestions automatiques                 ║
    ║  • Connexion analyse-chat intelligente                      ║
    ║  • Design responsive amélioré                               ║
    ║  • Performance optimisée                                    ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)

def check_requirements():
    """Vérifie que les dépendances sont installées"""
    print("🔍 Vérification des dépendances...")
    
    required_packages = [
        'flask',
        'requests',
        'python-dotenv',
        'pillow'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            missing_packages.append(package)
            print(f"  ❌ {package}")
    
    if missing_packages:
        print(f"\n⚠️  Packages manquants: {', '.join(missing_packages)}")
        print("📦 Installation automatique...")
        
        for package in missing_packages:
            try:
                subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
                print(f"  ✅ {package} installé")
            except subprocess.CalledProcessError:
                print(f"  ❌ Erreur lors de l'installation de {package}")
                return False
    
    return True

def setup_environment():
    """Configure l'environnement"""
    print("\n⚙️  Configuration de l'environnement...")
    
    # Créer le dossier logs s'il n'existe pas
    logs_dir = Path("logs")
    if not logs_dir.exists():
        logs_dir.mkdir()
        print("  ✅ Dossier logs créé")
    
    # Vérifier le fichier .env
    env_file = Path(".env")
    if not env_file.exists():
        print("  ⚠️  Fichier .env non trouvé, création d'un exemple...")
        
        env_content = """# Configuration MediScanAI Enhanced
GEMINI_API_KEY=your_gemini_api_key_here
FLASK_DEBUG=True
LOG_LEVEL=INFO

# Nouvelles configurations chat
CHAT_SESSION_TIMEOUT_HOURS=24
CHAT_MAX_CONTEXT_LENGTH=4000
CHAT_MAX_MESSAGES_IN_CONTEXT=10
CHAT_ENABLE_ANALYSIS_LINKING=True

# Performance
ENABLE_CACHING=True
CACHE_TIMEOUT=300

# Sécurité
RATE_LIMIT_PER_MINUTE=60
ENABLE_CORS=True
"""
        
        with open(env_file, 'w') as f:
            f.write(env_content)
        
        print("  ✅ Fichier .env créé avec les valeurs par défaut")
        print("  ⚠️  N'oubliez pas de configurer votre GEMINI_API_KEY")
    else:
        print("  ✅ Fichier .env trouvé")
    
    # Vérifier les dossiers static
    static_dirs = ['static/css', 'static/js', 'static/images']
    for dir_path in static_dirs:
        dir_obj = Path(dir_path)
        if not dir_obj.exists():
            dir_obj.mkdir(parents=True)
            print(f"  ✅ Dossier {dir_path} créé")

def check_files():
    """Vérifie que tous les fichiers nécessaires sont présents"""
    print("\n📁 Vérification des fichiers...")
    
    required_files = [
        'api/app.py',
        'api/gemini_service.py',
        'api/chat_manager.py',
        'api/config.py',
        'templates/index.html',
        'static/css/enhanced-styles.css',
        'static/js/chat-enhancements.js'
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"  ✅ {file_path}")
        else:
            missing_files.append(file_path)
            print(f"  ❌ {file_path}")
    
    if missing_files:
        print(f"\n⚠️  Fichiers manquants: {len(missing_files)}")
        for file_path in missing_files:
            print(f"    - {file_path}")
        return False
    
    return True

def start_application():
    """Démarre l'application Flask"""
    print("\n🚀 Démarrage de l'application...")
    
    # Changer vers le répertoire de l'API
    os.chdir('api')
    
    # Variables d'environnement pour Flask
    env = os.environ.copy()
    env['FLASK_APP'] = 'app.py'
    env['FLASK_ENV'] = 'development'
    
    try:
        print("  📡 Serveur Flask en cours de démarrage...")
        print("  🌐 URL: http://localhost:5000")
        print("  📊 Dashboard: http://localhost:5000/")
        print("  🤖 Chat: Cliquez sur l'icône de chat en bas à droite")
        print("\n  💡 Conseils:")
        print("    • Testez d'abord l'analyse d'image")
        print("    • Puis cliquez sur 'Discuter de cette analyse'")
        print("    • Profitez de la nouvelle interface de chat!")
        print("\n  🛑 Pour arrêter: Ctrl+C")
        print("  " + "="*50)
        
        # Démarrer Flask
        subprocess.run([sys.executable, 'app.py'], env=env)
        
    except KeyboardInterrupt:
        print("\n\n🛑 Application arrêtée par l'utilisateur")
    except Exception as e:
        print(f"\n❌ Erreur lors du démarrage: {e}")
        return False
    
    return True

def run_tests():
    """Lance les tests des améliorations"""
    print("\n🧪 Lancement des tests...")
    
    # Retourner au répertoire racine
    os.chdir('..')
    
    try:
        # Attendre que le serveur soit prêt
        print("  ⏳ Attente du démarrage du serveur...")
        time.sleep(3)
        
        # Lancer les tests
        subprocess.run([sys.executable, 'test_enhancements.py'])
        
    except Exception as e:
        print(f"❌ Erreur lors des tests: {e}")

def main():
    """Fonction principale"""
    print_banner()
    
    # Vérifications préliminaires
    if not check_requirements():
        print("❌ Impossible de continuer sans les dépendances requises")
        sys.exit(1)
    
    setup_environment()
    
    if not check_files():
        print("❌ Fichiers manquants. Assurez-vous d'avoir tous les fichiers du projet.")
        sys.exit(1)
    
    print("\n✅ Toutes les vérifications sont passées!")
    
    # Demander à l'utilisateur ce qu'il veut faire
    print("\n🎯 Que voulez-vous faire?")
    print("  1. Démarrer l'application (recommandé)")
    print("  2. Lancer les tests seulement")
    print("  3. Démarrer l'application puis lancer les tests")
    print("  4. Quitter")
    
    try:
        choice = input("\nVotre choix (1-4): ").strip()
        
        if choice == '1':
            start_application()
        elif choice == '2':
            run_tests()
        elif choice == '3':
            print("🚀 Démarrage de l'application en arrière-plan...")
            # TODO: Implémenter le démarrage en arrière-plan
            print("⚠️  Fonctionnalité non encore implémentée")
            print("💡 Démarrez l'application dans un terminal séparé, puis lancez les tests")
        elif choice == '4':
            print("👋 Au revoir!")
        else:
            print("❌ Choix invalide")
            
    except KeyboardInterrupt:
        print("\n\n👋 Au revoir!")

if __name__ == "__main__":
    main()
