/* Design System Professionnel pour MediScanAI */

/* Variables CSS Design System */
:root {
    /* Palette de couleurs principale */
    --primary-50: #eff6ff;
    --primary-100: #dbeafe;
    --primary-200: #bfdbfe;
    --primary-300: #93c5fd;
    --primary-400: #60a5fa;
    --primary-500: #3b82f6;
    --primary-600: #2563eb;
    --primary-700: #1d4ed8;
    --primary-800: #1e40af;
    --primary-900: #1e3a8a;

    /* Couleurs secondaires */
    --secondary-50: #f8fafc;
    --secondary-100: #f1f5f9;
    --secondary-200: #e2e8f0;
    --secondary-300: #cbd5e1;
    --secondary-400: #94a3b8;
    --secondary-500: #64748b;
    --secondary-600: #475569;
    --secondary-700: #334155;
    --secondary-800: #1e293b;
    --secondary-900: #0f172a;

    /* Couleurs d'accent médical */
    --medical-primary: #0ea5e9;
    --medical-secondary: #06b6d4;
    --medical-accent: #8b5cf6;
    --medical-success: #10b981;
    --medical-warning: #f59e0b;
    --medical-error: #ef4444;

    /* Gradients professionnels */
    --gradient-primary: linear-gradient(135deg, var(--primary-600), var(--primary-700));
    --gradient-secondary: linear-gradient(135deg, var(--medical-primary), var(--medical-secondary));
    --gradient-accent: linear-gradient(135deg, var(--medical-accent), var(--primary-600));
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));

    /* Ombres professionnelles */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);

    /* Espacements */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    --spacing-3xl: 4rem;

    /* Rayons de bordure */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-2xl: 1.5rem;
    --radius-full: 9999px;

    /* Typographie */
    --font-sans: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-mono: 'JetBrains Mono', 'Fira Code', monospace;

    /* Transitions */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 300ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 500ms cubic-bezier(0.4, 0, 0.2, 1);

    /* Z-index */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;
}

/* Reset et base améliorés */
*, *::before, *::after {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
}

body {
    font-family: var(--font-sans);
    line-height: 1.6;
    color: var(--secondary-800);
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Animations professionnelles */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

@keyframes glow {
    0%, 100% { box-shadow: 0 0 20px rgba(59, 130, 246, 0.3); }
    50% { box-shadow: 0 0 30px rgba(59, 130, 246, 0.6); }
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Composants professionnels de base */
.glass-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    transition: all var(--transition-normal);
}

.glass-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-2xl);
}

.gradient-card {
    background: var(--gradient-primary);
    color: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-lg);
    position: relative;
    overflow: hidden;
}

.gradient-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left var(--transition-slow);
}

.gradient-card:hover::before {
    left: 100%;
}

.medical-card {
    background: white;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-md);
    border-left: 4px solid var(--medical-primary);
    transition: all var(--transition-normal);
    position: relative;
}

.medical-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-left-color: var(--medical-secondary);
}

.medical-card::after {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 4px;
    height: 100%;
    background: var(--gradient-secondary);
    border-radius: 0 var(--radius-xl) var(--radius-xl) 0;
    opacity: 0;
    transition: opacity var(--transition-normal);
}

.medical-card:hover::after {
    opacity: 1;
}

/* Boutons professionnels */
.btn-primary {
    background: var(--gradient-primary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: var(--shadow-sm);
}

.btn-secondary {
    background: white;
    color: var(--primary-600);
    border: 2px solid var(--primary-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.btn-secondary:hover {
    background: var(--primary-50);
    border-color: var(--primary-300);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-medical {
    background: var(--gradient-secondary);
    color: white;
    border: none;
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 600;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    cursor: pointer;
    box-shadow: var(--shadow-md);
}

.btn-medical:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    filter: brightness(1.1);
}

.btn-ghost {
    background: transparent;
    color: var(--secondary-600);
    border: 1px solid var(--secondary-200);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md) var(--spacing-xl);
    font-weight: 500;
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    cursor: pointer;
}

.btn-ghost:hover {
    background: var(--secondary-50);
    border-color: var(--secondary-300);
    color: var(--secondary-700);
}

/* Inputs professionnels */
.input-professional {
    width: 100%;
    padding: var(--spacing-md) var(--spacing-lg);
    border: 2px solid var(--secondary-200);
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    transition: all var(--transition-normal);
    background: white;
    color: var(--secondary-800);
}

.input-professional:focus {
    outline: none;
    border-color: var(--primary-500);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-professional::placeholder {
    color: var(--secondary-400);
}

/* Navigation professionnelle */
.nav-professional {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--secondary-200);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
    transition: all var(--transition-normal);
}

.nav-professional.scrolled {
    box-shadow: var(--shadow-lg);
    background: rgba(255, 255, 255, 0.98);
}

/* Styles pour le chatbot amélioré */
.chatbot-container {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbot-container.minimized {
    height: 60px !important;
    overflow: hidden;
}

.chatbot-container.minimized .chatbot-messages,
.chatbot-container.minimized .chat-input-area {
    display: none;
}

/* Messages de chat */
.chat-message {
    animation: slideInUp 0.3s ease-out;
    transform-origin: bottom;
    transition: all 0.2s ease;
}

.chat-message:hover {
    transform: translateY(-1px);
}

.chat-message.user-message {
    margin-left: auto;
    margin-right: 0;
    max-width: 80%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border-radius: 20px 20px 5px 20px;
    box-shadow: var(--shadow-md);
}

.chat-message.assistant-message {
    margin-left: 0;
    margin-right: auto;
    max-width: 85%;
    border-radius: 20px 20px 20px 5px;
    box-shadow: var(--shadow-sm);
}

.chat-message.error-message {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: white;
    border-radius: 20px;
    box-shadow: var(--shadow-md);
}

.chat-message.system-message {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    border-radius: 20px;
    text-align: center;
    margin: 0 auto;
    max-width: 90%;
    box-shadow: var(--shadow-md);
}

/* Scrollbar personnalisée */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border-radius: 10px;
    transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary), var(--primary));
}

/* Indicateur de frappe */
.typing-indicator {
    animation: fadeIn 0.3s ease-in;
}

.typing-dots {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary);
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

/* Boutons améliorés */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-enhanced:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Bouton de connexion analyse */
.analysis-connection-btn {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

.analysis-connection-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
}

.analysis-connection-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Zone d'analyse connectée */
.analysis-connected {
    border: 2px solid var(--success);
    background: rgba(16, 185, 129, 0.1);
    animation: slideInUp 0.5s ease-out;
}

/* Responsive design pour le chatbot */
@media (max-width: 768px) {
    .chatbot-container {
        width: calc(100vw - 2rem) !important;
        right: 1rem !important;
        left: 1rem !important;
        bottom: 5rem !important;
        max-width: none !important;
    }

    .chat-message {
        max-width: 95% !important;
    }

    .chat-message.user-message {
        max-width: 90% !important;
    }
}

/* Améliorations pour les résultats d'analyse */
.result-card-enhanced {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.status-badge.online {
    background-color: #10b981;
    color: white;
}

.status-badge.typing {
    background-color: var(--primary);
    color: white;
}

.status-badge.error {
    background-color: var(--error);
    color: white;
}

.status-badge.analysis-linked {
    background-color: #8b5cf6;
    color: white;
}

/* Améliorations pour les inputs */
.input-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e5e7eb;
}

.input-enhanced:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    outline: none;
}

/* Loader amélioré */
.loader-enhanced {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notifications toast */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid var(--success);
}

.toast-notification.error {
    border-left-color: var(--error);
}

.toast-notification.warning {
    border-left-color: var(--warning);
}

/* Améliorations pour l'accessibilité */
.focus-visible:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --light: #1f2937;
        --dark: #f9fafb;
    }

    .chatbot-container {
        background: #1f2937;
        border-color: #374151;
    }

    .chat-message.assistant-message {
        background: #374151;
        color: #f9fafb;
    }
}

/* Transitions fluides pour tous les éléments interactifs */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Désactiver les transitions pendant le redimensionnement */
.resize-animation-stopper * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}
