/* Styles améliorés pour MediScanAI */

/* Variables CSS personnalisées */
:root {
    --primary: #4361ee;
    --secondary: #3f37c9;
    --accent: #4cc9f0;
    --success: #10b981;
    --warning: #f59e0b;
    --error: #ef4444;
    --dark: #1a1a2e;
    --light: #f8f9fa;
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Animations personnalisées */
@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Styles pour le chatbot amélioré */
.chatbot-container {
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chatbot-container.minimized {
    height: 60px !important;
    overflow: hidden;
}

.chatbot-container.minimized .chatbot-messages,
.chatbot-container.minimized .chat-input-area {
    display: none;
}

/* Messages de chat */
.chat-message {
    animation: slideInUp 0.3s ease-out;
    transform-origin: bottom;
    transition: all 0.2s ease;
}

.chat-message:hover {
    transform: translateY(-1px);
}

.chat-message.user-message {
    margin-left: auto;
    margin-right: 0;
    max-width: 80%;
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    color: white;
    border-radius: 20px 20px 5px 20px;
    box-shadow: var(--shadow-md);
}

.chat-message.assistant-message {
    margin-left: 0;
    margin-right: auto;
    max-width: 85%;
    border-radius: 20px 20px 20px 5px;
    box-shadow: var(--shadow-sm);
}

.chat-message.error-message {
    background: linear-gradient(135deg, var(--error), #dc2626);
    color: white;
    border-radius: 20px;
    box-shadow: var(--shadow-md);
}

.chat-message.system-message {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    border-radius: 20px;
    text-align: center;
    margin: 0 auto;
    max-width: 90%;
    box-shadow: var(--shadow-md);
}

/* Scrollbar personnalisée */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    border-radius: 10px;
    transition: background 0.3s ease;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, var(--secondary), var(--primary));
}

/* Indicateur de frappe */
.typing-indicator {
    animation: fadeIn 0.3s ease-in;
}

.typing-dots {
    display: inline-flex;
    align-items: center;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: var(--primary);
    animation: bounce 1.4s infinite ease-in-out both;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }
.typing-dot:nth-child(3) { animation-delay: 0s; }

/* Boutons améliorés */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateZ(0);
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
}

.btn-enhanced:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Bouton de connexion analyse */
.analysis-connection-btn {
    background: linear-gradient(135deg, var(--success), #059669);
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 12px;
    font-weight: 600;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 4px 6px -1px rgba(16, 185, 129, 0.3);
    position: relative;
    overflow: hidden;
}

.analysis-connection-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(16, 185, 129, 0.4);
}

.analysis-connection-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* Zone d'analyse connectée */
.analysis-connected {
    border: 2px solid var(--success);
    background: rgba(16, 185, 129, 0.1);
    animation: slideInUp 0.5s ease-out;
}

/* Responsive design pour le chatbot */
@media (max-width: 768px) {
    .chatbot-container {
        width: calc(100vw - 2rem) !important;
        right: 1rem !important;
        left: 1rem !important;
        bottom: 5rem !important;
        max-width: none !important;
    }
    
    .chat-message {
        max-width: 95% !important;
    }
    
    .chat-message.user-message {
        max-width: 90% !important;
    }
}

/* Améliorations pour les résultats d'analyse */
.result-card-enhanced {
    background: white;
    border-radius: 16px;
    box-shadow: var(--shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.result-card-enhanced:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-xl);
}

/* Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.status-badge.online {
    background-color: #10b981;
    color: white;
}

.status-badge.typing {
    background-color: var(--primary);
    color: white;
}

.status-badge.error {
    background-color: var(--error);
    color: white;
}

.status-badge.analysis-linked {
    background-color: #8b5cf6;
    color: white;
}

/* Améliorations pour les inputs */
.input-enhanced {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid #e5e7eb;
}

.input-enhanced:focus {
    border-color: var(--primary);
    box-shadow: 0 0 0 3px rgba(67, 97, 238, 0.1);
    outline: none;
}

/* Loader amélioré */
.loader-enhanced {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: white;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Notifications toast */
.toast-notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: var(--shadow-xl);
    padding: 1rem 1.5rem;
    z-index: 1000;
    animation: slideInRight 0.3s ease-out;
    border-left: 4px solid var(--success);
}

.toast-notification.error {
    border-left-color: var(--error);
}

.toast-notification.warning {
    border-left-color: var(--warning);
}

/* Améliorations pour l'accessibilité */
.focus-visible:focus {
    outline: 2px solid var(--primary);
    outline-offset: 2px;
}

/* Mode sombre (optionnel) */
@media (prefers-color-scheme: dark) {
    :root {
        --light: #1f2937;
        --dark: #f9fafb;
    }
    
    .chatbot-container {
        background: #1f2937;
        border-color: #374151;
    }
    
    .chat-message.assistant-message {
        background: #374151;
        color: #f9fafb;
    }
}

/* Transitions fluides pour tous les éléments interactifs */
* {
    transition: color 0.2s ease, background-color 0.2s ease, border-color 0.2s ease, transform 0.2s ease, box-shadow 0.2s ease;
}

/* Désactiver les transitions pendant le redimensionnement */
.resize-animation-stopper * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
}
