/**
 * Améliorations du chat pour MediScanAI
 * Fonctionnalités avancées pour l'interface de chat
 */

// Configuration et constantes
const CHAT_CONFIG = {
    MAX_MESSAGE_LENGTH: 500,
    TYPING_DELAY: 1000,
    AUTO_SCROLL_DELAY: 100,
    RECONNECT_ATTEMPTS: 3,
    RECONNECT_DELAY: 2000
};

// État global du chat
let chatState = {
    isConnected: false,
    isTyping: false,
    messageCount: 0,
    sessionId: null,
    analysisLinked: false,
    lastActivity: null,
    reconnectAttempts: 0
};

/**
 * Initialise les améliorations du chat
 */
function initializeChatEnhancements() {
    console.log('Initialisation des améliorations du chat...');
    
    // Initialiser les gestionnaires d'événements
    setupEventListeners();
    
    // Initialiser l'état du chat
    updateChatStatus('online');
    
    // Démarrer le monitoring de l'activité
    startActivityMonitoring();
    
    console.log('Améliorations du chat initialisées avec succès');
}

/**
 * Configure les gestionnaires d'événements
 */
function setupEventListeners() {
    const chatInput = document.getElementById('chatbotInput');
    const sendButton = document.getElementById('sendChatbotMsg');
    
    if (chatInput) {
        // Gestionnaire pour le redimensionnement automatique
        chatInput.addEventListener('input', handleInputChange);
        
        // Gestionnaire pour les raccourcis clavier
        chatInput.addEventListener('keydown', handleKeyboardShortcuts);
        
        // Gestionnaire pour la détection de frappe
        chatInput.addEventListener('input', debounce(handleTypingIndicator, 300));
    }
    
    if (sendButton) {
        sendButton.addEventListener('click', handleSendMessage);
    }
    
    // Gestionnaire pour la visibilité de la page
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // Gestionnaire pour la fermeture de la page
    window.addEventListener('beforeunload', handlePageUnload);
}

/**
 * Gère les changements dans l'input de chat
 */
function handleInputChange(event) {
    const input = event.target;
    const length = input.value.length;
    
    // Mettre à jour le compteur de caractères
    updateCharacterCount(length);
    
    // Redimensionner automatiquement le textarea
    autoResizeTextarea(input);
    
    // Vérifier la limite de caractères
    if (length > CHAT_CONFIG.MAX_MESSAGE_LENGTH) {
        input.value = input.value.substring(0, CHAT_CONFIG.MAX_MESSAGE_LENGTH);
        showNotification('Limite de caractères atteinte', 'warning');
    }
}

/**
 * Gère les raccourcis clavier
 */
function handleKeyboardShortcuts(event) {
    // Ctrl/Cmd + Enter pour envoyer
    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        handleSendMessage();
        return;
    }
    
    // Shift + Enter pour nouvelle ligne (comportement par défaut)
    if (event.shiftKey && event.key === 'Enter') {
        return;
    }
    
    // Enter seul pour envoyer
    if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault();
        handleSendMessage();
        return;
    }
    
    // Échap pour fermer le chat
    if (event.key === 'Escape') {
        const chatContainer = document.getElementById('chatbotContainer');
        if (chatContainer && !chatContainer.classList.contains('hidden')) {
            toggleChatbot();
        }
    }
}

/**
 * Gère l'indicateur de frappe
 */
function handleTypingIndicator() {
    if (!chatState.isTyping) {
        chatState.isTyping = true;
        updateChatStatus('typing');
        
        // Arrêter l'indicateur après un délai
        setTimeout(() => {
            if (chatState.isTyping) {
                chatState.isTyping = false;
                updateChatStatus('online');
            }
        }, CHAT_CONFIG.TYPING_DELAY);
    }
}

/**
 * Gère l'envoi de message avec améliorations
 */
async function handleSendMessage() {
    const input = document.getElementById('chatbotInput');
    const message = input.value.trim();
    
    if (!message || chatState.isTyping) {
        return;
    }
    
    try {
        // Préparer l'interface
        chatState.isTyping = true;
        updateChatStatus('typing');
        disableInput(true);
        
        // Ajouter le message utilisateur
        addEnhancedUserMessage(message);
        
        // Vider l'input
        input.value = '';
        updateCharacterCount(0);
        autoResizeTextarea(input);
        
        // Afficher l'indicateur de frappe
        showEnhancedTypingIndicator();
        
        // Envoyer le message
        const response = await sendChatMessageToAPI(message);
        
        // Masquer l'indicateur de frappe
        hideEnhancedTypingIndicator();
        
        // Traiter la réponse
        if (response.success) {
            addEnhancedAssistantMessage(response.data.response, response.data.status);
            updateChatMetadata(response.data);
        } else {
            addEnhancedErrorMessage(response.error || 'Erreur de communication');
        }
        
    } catch (error) {
        console.error('Erreur lors de l\'envoi du message:', error);
        hideEnhancedTypingIndicator();
        addEnhancedErrorMessage('Une erreur inattendue s\'est produite');
    } finally {
        // Réactiver l'interface
        chatState.isTyping = false;
        updateChatStatus('online');
        disableInput(false);
        input.focus();
    }
}

/**
 * Envoie un message à l'API avec gestion d'erreurs améliorée
 */
async function sendChatMessageToAPI(message) {
    try {
        const response = await fetch('/api/chat', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                message: message,
                session_id: chatState.sessionId,
                model_type: currentModelType
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        // Mettre à jour l'état de la session
        if (data.session_id) {
            chatState.sessionId = data.session_id;
        }
        
        return { success: true, data };
        
    } catch (error) {
        console.error('Erreur API:', error);
        
        // Tentative de reconnexion si nécessaire
        if (chatState.reconnectAttempts < CHAT_CONFIG.RECONNECT_ATTEMPTS) {
            chatState.reconnectAttempts++;
            await new Promise(resolve => setTimeout(resolve, CHAT_CONFIG.RECONNECT_DELAY));
            return sendChatMessageToAPI(message);
        }
        
        return { success: false, error: error.message };
    }
}

/**
 * Ajoute un message utilisateur avec design amélioré
 */
function addEnhancedUserMessage(message) {
    const messagesContainer = document.getElementById('chatbotMessages');
    const messageElement = document.createElement('div');
    
    messageElement.className = 'chat-message user-message p-4 mb-4 shadow-sm';
    messageElement.innerHTML = `
        <div class="flex items-end justify-end space-x-2">
            <div class="flex-1 text-right">
                <div class="text-sm leading-relaxed">${escapeHtml(message)}</div>
                <span class="text-xs opacity-70 mt-1 block">Vous • ${getCurrentTime()}</span>
            </div>
            <div class="w-8 h-8 bg-white bg-opacity-20 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-user text-xs"></i>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    chatState.messageCount++;
    updateMessageCount();
    scrollToBottom();
}

/**
 * Ajoute un message assistant avec design amélioré
 */
function addEnhancedAssistantMessage(message, status = 'success') {
    const messagesContainer = document.getElementById('chatbotMessages');
    const messageElement = document.createElement('div');
    
    const statusClass = status === 'success' 
        ? 'assistant-message bg-gradient-to-r from-blue-100 to-blue-50 text-blue-800 border border-blue-200'
        : 'assistant-message bg-gradient-to-r from-yellow-100 to-yellow-50 text-yellow-800 border border-yellow-200';
    
    messageElement.className = `chat-message ${statusClass} rounded-2xl p-4 mb-4 shadow-sm`;
    messageElement.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-robot text-white text-xs"></i>
            </div>
            <div class="flex-1">
                <div class="text-sm leading-relaxed">${formatMessage(message)}</div>
                <span class="text-xs opacity-70 mt-2 block">Assistant • ${getCurrentTime()}</span>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    chatState.messageCount++;
    updateMessageCount();
    scrollToBottom();
}

/**
 * Ajoute un message d'erreur avec design amélioré
 */
function addEnhancedErrorMessage(message) {
    const messagesContainer = document.getElementById('chatbotMessages');
    const messageElement = document.createElement('div');
    
    messageElement.className = 'chat-message error-message rounded-2xl p-4 mb-4 shadow-sm';
    messageElement.innerHTML = `
        <div class="flex items-start space-x-3">
            <div class="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center flex-shrink-0">
                <i class="fas fa-exclamation-triangle text-white text-xs"></i>
            </div>
            <div class="flex-1">
                <div class="text-sm leading-relaxed">${escapeHtml(message)}</div>
                <span class="text-xs opacity-70 mt-2 block">Erreur • ${getCurrentTime()}</span>
            </div>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    scrollToBottom();
}

/**
 * Affiche l'indicateur de frappe amélioré
 */
function showEnhancedTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.classList.remove('hidden');
    }
}

/**
 * Masque l'indicateur de frappe amélioré
 */
function hideEnhancedTypingIndicator() {
    const indicator = document.getElementById('typingIndicator');
    if (indicator) {
        indicator.classList.add('hidden');
    }
}

/**
 * Met à jour le statut du chat
 */
function updateChatStatus(status) {
    const statusElement = document.getElementById('chatStatus');
    if (!statusElement) return;
    
    const statusConfig = {
        online: {
            class: 'text-xs bg-green-500 text-white px-2 py-0.5 rounded-full flex items-center',
            html: '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-pulse"></div>En ligne'
        },
        typing: {
            class: 'text-xs bg-blue-500 text-white px-2 py-0.5 rounded-full flex items-center',
            html: '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1 animate-bounce"></div>Réfléchit...'
        },
        error: {
            class: 'text-xs bg-red-500 text-white px-2 py-0.5 rounded-full flex items-center',
            html: '<div class="w-1.5 h-1.5 bg-white rounded-full mr-1"></div>Erreur'
        }
    };
    
    const config = statusConfig[status] || statusConfig.online;
    statusElement.className = config.class;
    statusElement.innerHTML = config.html;
}

/**
 * Met à jour le compteur de messages
 */
function updateMessageCount() {
    const countElement = document.getElementById('messageCount');
    if (countElement) {
        countElement.textContent = `${chatState.messageCount} message${chatState.messageCount > 1 ? 's' : ''}`;
    }
}

/**
 * Met à jour le compteur de caractères
 */
function updateCharacterCount(length) {
    const countElement = document.getElementById('charCount');
    if (countElement) {
        countElement.textContent = `${length}/${CHAT_CONFIG.MAX_MESSAGE_LENGTH}`;
        
        if (length > CHAT_CONFIG.MAX_MESSAGE_LENGTH * 0.9) {
            countElement.classList.add('text-red-500');
            countElement.classList.remove('text-gray-400');
        } else {
            countElement.classList.add('text-gray-400');
            countElement.classList.remove('text-red-500');
        }
    }
}

/**
 * Redimensionne automatiquement le textarea
 */
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    const maxHeight = 120; // 5 lignes environ
    const newHeight = Math.min(textarea.scrollHeight, maxHeight);
    textarea.style.height = newHeight + 'px';
}

/**
 * Active/désactive l'input de chat
 */
function disableInput(disabled) {
    const input = document.getElementById('chatbotInput');
    const button = document.getElementById('sendChatbotMsg');
    
    if (input) input.disabled = disabled;
    if (button) button.disabled = disabled;
}

/**
 * Met à jour les métadonnées du chat
 */
function updateChatMetadata(data) {
    if (data.has_analysis !== undefined) {
        chatState.analysisLinked = data.has_analysis;
        updateAnalysisIndicator(data.has_analysis);
    }
    
    if (data.message_count !== undefined) {
        chatState.messageCount = data.message_count;
        updateMessageCount();
    }
    
    chatState.lastActivity = new Date();
}

/**
 * Met à jour l'indicateur d'analyse liée
 */
function updateAnalysisIndicator(hasAnalysis) {
    const indicator = document.getElementById('analysisIndicator');
    if (indicator) {
        if (hasAnalysis) {
            indicator.classList.remove('hidden');
        } else {
            indicator.classList.add('hidden');
        }
    }
}

/**
 * Connecte une analyse au chat
 */
async function connectAnalysisToChat(analysisData, imageInfo) {
    try {
        const response = await fetch('/api/chat/connect-analysis', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                session_id: chatState.sessionId,
                analysis_data: analysisData,
                image_info: imageInfo,
                model_type: currentModelType
            })
        });
        
        if (response.ok) {
            const data = await response.json();
            chatState.sessionId = data.session_id;
            chatState.analysisLinked = true;
            
            // Afficher un message de confirmation
            addSystemMessage('Analyse d\'image connectée au chat');
            updateAnalysisIndicator(true);
            
            // Mettre à jour le placeholder
            const input = document.getElementById('chatbotInput');
            if (input) {
                input.placeholder = 'Posez vos questions sur l\'analyse...';
            }
            
            showNotification('Analyse connectée avec succès', 'success');
            return true;
        } else {
            throw new Error('Erreur lors de la connexion');
        }
    } catch (error) {
        console.error('Erreur de connexion analyse-chat:', error);
        showNotification('Erreur lors de la connexion de l\'analyse', 'error');
        return false;
    }
}

/**
 * Ajoute un message système
 */
function addSystemMessage(message) {
    const messagesContainer = document.getElementById('chatbotMessages');
    const messageElement = document.createElement('div');
    
    messageElement.className = 'chat-message system-message rounded-2xl p-3 mb-4 text-center';
    messageElement.innerHTML = `
        <div class="flex items-center justify-center space-x-2 text-white">
            <i class="fas fa-link"></i>
            <span class="text-sm font-medium">${escapeHtml(message)}</span>
        </div>
    `;
    
    messagesContainer.appendChild(messageElement);
    scrollToBottom();
}

/**
 * Affiche une notification toast
 */
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `toast-notification ${type}`;
    notification.innerHTML = `
        <div class="flex items-center space-x-2">
            <i class="fas fa-${type === 'success' ? 'check-circle' : type === 'error' ? 'exclamation-circle' : 'info-circle'}"></i>
            <span>${escapeHtml(message)}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Supprimer après 3 secondes
    setTimeout(() => {
        notification.remove();
    }, 3000);
}

/**
 * Démarre le monitoring de l'activité
 */
function startActivityMonitoring() {
    setInterval(() => {
        if (chatState.lastActivity) {
            const timeSinceActivity = Date.now() - chatState.lastActivity.getTime();
            
            // Nettoyer les sessions inactives après 30 minutes
            if (timeSinceActivity > 30 * 60 * 1000) {
                // Logique de nettoyage si nécessaire
            }
        }
    }, 60000); // Vérifier chaque minute
}

/**
 * Gère les changements de visibilité de la page
 */
function handleVisibilityChange() {
    if (document.hidden) {
        // Page cachée - réduire l'activité
        chatState.isConnected = false;
    } else {
        // Page visible - reprendre l'activité
        chatState.isConnected = true;
        updateChatStatus('online');
    }
}

/**
 * Gère la fermeture de la page
 */
function handlePageUnload() {
    // Sauvegarder l'état si nécessaire
    if (chatState.sessionId) {
        localStorage.setItem('mediscan_chat_session', chatState.sessionId);
    }
}

/**
 * Fonction utilitaire pour debounce
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

/**
 * Fait défiler vers le bas
 */
function scrollToBottom() {
    const messagesContainer = document.getElementById('chatbotMessages');
    if (messagesContainer) {
        setTimeout(() => {
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }, CHAT_CONFIG.AUTO_SCROLL_DELAY);
    }
}

/**
 * Obtient l'heure actuelle formatée
 */
function getCurrentTime() {
    return new Date().toLocaleTimeString('fr-FR', {
        hour: '2-digit',
        minute: '2-digit'
    });
}

/**
 * Échappe le HTML pour éviter les injections XSS
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

/**
 * Formate un message avec support markdown basique
 */
function formatMessage(message) {
    return message
        .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
        .replace(/\*(.*?)\*/g, '<em>$1</em>')
        .replace(/\n/g, '<br>')
        .replace(/`(.*?)`/g, '<code class="bg-gray-200 px-1 rounded">$1</code>');
}

// Initialiser les améliorations quand le DOM est prêt
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeChatEnhancements);
} else {
    initializeChatEnhancements();
}
