#!/usr/bin/env python3
"""
Script de test pour les améliorations de MediScanAI
Teste les nouvelles fonctionnalités du chat et de l'intégration analyse-chat
"""

import requests
import json
import time
import sys
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:5000"
TEST_SESSION_ID = None

def print_test_header(test_name):
    """Affiche l'en-tête d'un test"""
    print(f"\n{'='*60}")
    print(f"🧪 TEST: {test_name}")
    print(f"{'='*60}")

def print_success(message):
    """Affiche un message de succès"""
    print(f"✅ {message}")

def print_error(message):
    """Affiche un message d'erreur"""
    print(f"❌ {message}")

def print_info(message):
    """Affiche un message d'information"""
    print(f"ℹ️  {message}")

def test_chat_endpoint():
    """Teste l'endpoint de chat amélioré"""
    global TEST_SESSION_ID
    
    print_test_header("Endpoint de Chat Amélioré")
    
    try:
        # Test 1: Envoi d'un message simple
        payload = {
            "message": "Bonjour, pouvez-vous m'expliquer ce qu'est une IRM ?",
            "model_type": "brain"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            TEST_SESSION_ID = data.get('session_id')
            
            print_success(f"Message envoyé avec succès")
            print_info(f"Session ID: {TEST_SESSION_ID}")
            print_info(f"Réponse: {data.get('response', '')[:100]}...")
            print_info(f"Nombre de messages: {data.get('message_count', 0)}")
            print_info(f"Analyse liée: {data.get('has_analysis', False)}")
            
            # Vérifier les métadonnées
            metadata = data.get('response_metadata', {})
            if metadata:
                print_info(f"Longueur réponse: {metadata.get('response_length', 0)}")
                print_info(f"Contexte utilisé: {metadata.get('context_used', False)}")
            
        else:
            print_error(f"Erreur HTTP {response.status_code}: {response.text}")
            
    except Exception as e:
        print_error(f"Erreur lors du test de chat: {e}")

def test_chat_session_management():
    """Teste la gestion des sessions de chat"""
    print_test_header("Gestion des Sessions de Chat")
    
    try:
        # Test 1: Création d'une nouvelle session
        payload = {
            "action": "create",
            "model_type": "oral"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat/session", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            new_session_id = data.get('session_id')
            print_success(f"Nouvelle session créée: {new_session_id}")
        else:
            print_error(f"Erreur création session: {response.status_code}")
        
        # Test 2: Effacement de l'historique
        if TEST_SESSION_ID:
            payload = {
                "action": "clear",
                "session_id": TEST_SESSION_ID
            }
            
            response = requests.post(f"{BASE_URL}/api/chat/session", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                print_success(f"Historique effacé: {data.get('success', False)}")
            else:
                print_error(f"Erreur effacement: {response.status_code}")
                
    except Exception as e:
        print_error(f"Erreur lors du test de gestion de session: {e}")

def test_analysis_chat_connection():
    """Teste la connexion analyse-chat"""
    print_test_header("Connexion Analyse-Chat")
    
    try:
        # Données d'analyse simulées
        analysis_data = {
            "class_name": "Tumeur bénigne",
            "confidence": 85.5,
            "description": "Tumeur bénigne détectée avec une confiance élevée",
            "summary": "Analyse d'IRM cérébrale montrant une masse bénigne"
        }
        
        image_info = {
            "timestamp": datetime.now().isoformat(),
            "filename": "test_brain_scan.jpg",
            "size": "512x512"
        }
        
        payload = {
            "session_id": TEST_SESSION_ID,
            "analysis_data": analysis_data,
            "image_info": image_info,
            "model_type": "brain"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat/connect-analysis", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print_success(f"Analyse connectée au chat")
            print_info(f"Session ID: {data.get('session_id')}")
            print_info(f"Message: {data.get('message')}")
            
            # Vérifier le résumé d'analyse
            summary = data.get('analysis_summary', {})
            if summary:
                print_info(f"Résumé - Classe: {summary.get('class_name')}")
                print_info(f"Résumé - Confiance: {summary.get('confidence')}%")
                print_info(f"Résumé - Type: {summary.get('model_type')}")
        else:
            print_error(f"Erreur connexion analyse: {response.status_code}")
            print_error(f"Réponse: {response.text}")
            
    except Exception as e:
        print_error(f"Erreur lors du test de connexion analyse: {e}")

def test_chat_with_analysis_context():
    """Teste le chat avec contexte d'analyse"""
    print_test_header("Chat avec Contexte d'Analyse")
    
    try:
        # Envoyer un message qui fait référence à l'analyse
        payload = {
            "message": "Pouvez-vous m'expliquer plus en détail cette tumeur bénigne ?",
            "session_id": TEST_SESSION_ID,
            "model_type": "brain"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        
        if response.status_code == 200:
            data = response.json()
            print_success(f"Message avec contexte envoyé")
            print_info(f"Analyse liée: {data.get('has_analysis', False)}")
            print_info(f"Réponse: {data.get('response', '')[:150]}...")
            
            # Vérifier que l'analyse est bien liée
            if data.get('has_analysis'):
                summary = data.get('analysis_summary', {})
                print_success(f"Contexte d'analyse utilisé: {summary.get('class_name')}")
            else:
                print_error("Aucun contexte d'analyse trouvé")
                
        else:
            print_error(f"Erreur chat avec contexte: {response.status_code}")
            
    except Exception as e:
        print_error(f"Erreur lors du test de chat avec contexte: {e}")

def test_removed_suggestions_endpoint():
    """Vérifie que l'endpoint des suggestions a bien été supprimé"""
    print_test_header("Vérification Suppression Suggestions")
    
    try:
        response = requests.get(f"{BASE_URL}/api/chat/suggestions?model_type=brain")
        
        if response.status_code == 404:
            print_success("Endpoint des suggestions correctement supprimé (404)")
        elif response.status_code == 405:
            print_success("Endpoint des suggestions correctement supprimé (405)")
        else:
            print_error(f"Endpoint des suggestions encore actif: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print_error("Impossible de se connecter au serveur")
    except Exception as e:
        print_error(f"Erreur lors du test de suppression: {e}")

def test_error_handling():
    """Teste la gestion d'erreurs améliorée"""
    print_test_header("Gestion d'Erreurs Améliorée")
    
    try:
        # Test 1: Message vide
        payload = {
            "message": "",
            "model_type": "brain"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        print_info(f"Message vide - Status: {response.status_code}")
        
        # Test 2: Type de modèle invalide
        payload = {
            "message": "Test message",
            "model_type": "invalid_model"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        print_info(f"Modèle invalide - Status: {response.status_code}")
        
        # Test 3: Session ID invalide
        payload = {
            "message": "Test message",
            "session_id": "invalid_session_id",
            "model_type": "brain"
        }
        
        response = requests.post(f"{BASE_URL}/api/chat", json=payload)
        if response.status_code == 200:
            data = response.json()
            print_success(f"Session invalide gérée - Nouvelle session: {data.get('session_id')}")
        
        print_success("Tests de gestion d'erreurs terminés")
        
    except Exception as e:
        print_error(f"Erreur lors du test de gestion d'erreurs: {e}")

def test_performance():
    """Teste les performances du chat"""
    print_test_header("Test de Performance")
    
    try:
        messages = [
            "Qu'est-ce qu'une IRM ?",
            "Comment fonctionne l'imagerie médicale ?",
            "Quels sont les risques d'une tumeur cérébrale ?",
            "Comment interpréter les résultats d'analyse ?",
            "Merci pour vos explications"
        ]
        
        total_time = 0
        successful_requests = 0
        
        for i, message in enumerate(messages, 1):
            start_time = time.time()
            
            payload = {
                "message": message,
                "session_id": TEST_SESSION_ID,
                "model_type": "brain"
            }
            
            response = requests.post(f"{BASE_URL}/api/chat", json=payload)
            
            end_time = time.time()
            request_time = end_time - start_time
            total_time += request_time
            
            if response.status_code == 200:
                successful_requests += 1
                print_info(f"Message {i}/5 - Temps: {request_time:.2f}s")
            else:
                print_error(f"Message {i}/5 - Erreur: {response.status_code}")
            
            # Petite pause entre les requêtes
            time.sleep(0.5)
        
        avg_time = total_time / len(messages)
        success_rate = (successful_requests / len(messages)) * 100
        
        print_success(f"Performance - Temps moyen: {avg_time:.2f}s")
        print_success(f"Performance - Taux de succès: {success_rate:.1f}%")
        
    except Exception as e:
        print_error(f"Erreur lors du test de performance: {e}")

def run_all_tests():
    """Exécute tous les tests"""
    print(f"\n🚀 DÉBUT DES TESTS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"URL de base: {BASE_URL}")
    
    # Vérifier que le serveur est accessible
    try:
        response = requests.get(f"{BASE_URL}/")
        if response.status_code != 200:
            print_error(f"Serveur non accessible: {response.status_code}")
            return
    except requests.exceptions.ConnectionError:
        print_error("Impossible de se connecter au serveur. Assurez-vous qu'il est démarré.")
        return
    
    print_success("Serveur accessible")
    
    # Exécuter les tests
    test_chat_endpoint()
    test_chat_session_management()
    test_analysis_chat_connection()
    test_chat_with_analysis_context()
    test_removed_suggestions_endpoint()
    test_error_handling()
    test_performance()
    
    print(f"\n🏁 TESTS TERMINÉS - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Session de test utilisée: {TEST_SESSION_ID}")

if __name__ == "__main__":
    # Permettre de spécifier une URL différente
    if len(sys.argv) > 1:
        BASE_URL = sys.argv[1].rstrip('/')
    
    run_all_tests()
