#!/usr/bin/env python3
"""
Script de test pour vérifier que les fichiers statiques sont accessibles
"""

import requests
import sys

def test_static_files():
    """Teste l'accès aux fichiers statiques"""
    base_url = "http://localhost:5000"
    
    files_to_test = [
        "/static/css/tailwind-local.css",
        "/static/css/enhanced-styles.css", 
        "/static/js/chat-enhancements.js",
        "/favicon.ico"
    ]
    
    print("🧪 Test d'accès aux fichiers statiques")
    print("=" * 50)
    
    for file_path in files_to_test:
        url = base_url + file_path
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ {file_path} - OK ({len(response.content)} bytes)")
            else:
                print(f"❌ {file_path} - Erreur {response.status_code}")
        except requests.exceptions.ConnectionError:
            print(f"🔌 Serveur non accessible sur {base_url}")
            print("💡 Assurez-vous que le serveur Flask est démarré")
            return False
        except Exception as e:
            print(f"❌ {file_path} - Erreur: {e}")
    
    return True

def test_main_page():
    """Teste l'accès à la page principale"""
    base_url = "http://localhost:5000"
    
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            print(f"✅ Page principale - OK ({len(response.content)} bytes)")
            
            # Vérifier que les liens CSS sont corrects
            content = response.text
            if "/static/css/tailwind-local.css" in content:
                print("✅ Lien Tailwind CSS correct")
            else:
                print("❌ Lien Tailwind CSS incorrect")
                
            if "/static/css/enhanced-styles.css" in content:
                print("✅ Lien Enhanced CSS correct")
            else:
                print("❌ Lien Enhanced CSS incorrect")
                
            if "/static/js/chat-enhancements.js" in content:
                print("✅ Lien JavaScript correct")
            else:
                print("❌ Lien JavaScript incorrect")
                
            return True
        else:
            print(f"❌ Page principale - Erreur {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Page principale - Erreur: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Test des fichiers statiques MediScanAI")
    print()
    
    # Test de la page principale
    if test_main_page():
        print()
        # Test des fichiers statiques
        test_static_files()
    
    print("\n💡 Si des erreurs persistent:")
    print("  1. Redémarrez le serveur Flask")
    print("  2. Vérifiez que vous êtes dans le bon répertoire")
    print("  3. Videz le cache du navigateur")
