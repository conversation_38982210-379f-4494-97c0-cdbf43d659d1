import unittest
import sys
import os
import json
import base64
from PIL import Image
import io

# Add the api directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'api'))

from app import app
from config import Config

class TestMRIDetection(unittest.TestCase):
    """Test cases for MRI detection functionality"""
    
    def setUp(self):
        """Set up test client"""
        self.app = app.test_client()
        self.app.testing = True
        
    def create_test_image(self, size=(224, 224), color='RGB'):
        """Create a test image for testing"""
        image = Image.new(color, size, color=(128, 128, 128))
        return image
    
    def image_to_base64(self, image, format='JPEG'):
        """Convert PIL image to base64 string"""
        buffer = io.BytesIO()
        image.save(buffer, format=format)
        img_str = base64.b64encode(buffer.getvalue()).decode()
        return f"data:image/{format.lower()};base64,{img_str}"
    
    def test_mri_detection_endpoint_exists(self):
        """Test that the MRI detection endpoint exists"""
        response = self.app.post('/api/detect-mri')
        # Should return 400 (bad request) not 404 (not found)
        self.assertNotEqual(response.status_code, 404)
    
    def test_mri_detection_no_data(self):
        """Test MRI detection with no image data"""
        response = self.app.post('/api/detect-mri', json={})
        self.assertEqual(response.status_code, 400)
        data = json.loads(response.data)
        self.assertIn('error', data)
    
    def test_mri_detection_base64(self):
        """Test MRI detection with base64 image data"""
        test_image = self.create_test_image()
        base64_image = self.image_to_base64(test_image)
        
        response = self.app.post('/api/detect-mri', json={
            'image': base64_image,
            'format': 'JPEG'
        })
        
        # Should not return 500 error
        self.assertNotEqual(response.status_code, 500)
        
        if response.status_code == 200:
            data = json.loads(response.data)
            # Check required fields
            self.assertIn('is_mri', data)
            self.assertIn('confidence', data)
            self.assertIn('detected_type', data)
            self.assertIn('api_status', data)
            
            # Check data types
            self.assertIsInstance(data['is_mri'], bool)
            self.assertIsInstance(data['confidence'], (int, float))
            self.assertGreaterEqual(data['confidence'], 0.0)
            self.assertLessEqual(data['confidence'], 1.0)
    
    def test_config_validation(self):
        """Test that configuration is properly loaded"""
        self.assertTrue(hasattr(Config, 'GEMINI_API_KEY'))
        self.assertTrue(hasattr(Config, 'MRI_DETECTION_CONFIDENCE_THRESHOLD'))
        self.assertIsInstance(Config.MRI_DETECTION_CONFIDENCE_THRESHOLD, float)
    
    def test_allowed_file_extensions(self):
        """Test that allowed file extensions are properly configured"""
        allowed_extensions = Config.ALLOWED_EXTENSIONS
        expected_extensions = {'png', 'jpg', 'jpeg', 'webp'}
        self.assertEqual(allowed_extensions, expected_extensions)

if __name__ == '__main__':
    # Create logs directory if it doesn't exist
    os.makedirs('logs', exist_ok=True)
    
    # Run tests
    unittest.main()
